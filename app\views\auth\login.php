<?php
$bodyClass = 'bg-light d-flex align-items-center min-vh-100';
$content = ob_get_clean();
ob_start();
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <!-- Logo/Brand -->
                    <div class="text-center mb-4">
                        <i class="fas fa-server fa-3x text-primary mb-3"></i>
                        <h3 class="card-title"><?= e($config['app']['name']) ?></h3>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    <!-- Login Form -->
                    <form method="POST" action="<?= url('/login') ?>" id="loginForm">
                        <?= csrf_field() ?>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" 
                                       class="form-control <?= has_error('username') ? 'is-invalid' : '' ?>" 
                                       id="username" 
                                       name="username" 
                                       value="<?= e(old('username')) ?>"
                                       placeholder="Enter username or email"
                                       required 
                                       autofocus>
                                <?php if (has_error('username')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('username')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control <?= has_error('password') ? 'is-invalid' : '' ?>" 
                                       id="password" 
                                       name="password" 
                                       placeholder="Enter password"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if (has_error('password')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('password')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </button>
                        </div>
                    </form>
                    
                    <!-- Additional Links -->
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            Forgot your password? 
                            <a href="<?= url('/forgot-password') ?>" class="text-decoration-none">Reset it here</a>
                        </small>
                    </div>
                </div>
                
                <!-- System Info -->
                <div class="card-footer bg-light text-center">
                    <small class="text-muted">
                        Version <?= e($config['app']['version']) ?> | 
                        <span id="systemStatus" class="text-success">
                            <i class="fas fa-circle"></i> System Online
                        </span>
                    </small>
                </div>
            </div>
            
            <!-- Environment Indicator -->
            <?php if (DEBUG_MODE): ?>
                <div class="alert alert-warning mt-3 text-center">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Development Mode</strong> - Debug information is enabled
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .min-vh-100 {
        min-height: 100vh;
    }
    
    .card {
        border: none;
        border-radius: 10px;
    }
    
    .card-body {
        padding: 2rem;
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }
    
    .form-control {
        border-left: none;
    }
    
    .form-control:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(45deg, #0056b3, #004085);
    }
    
    .fa-3x {
        font-size: 3rem;
    }
    
    @media (max-width: 576px) {
        .card-body {
            padding: 1.5rem;
        }
    }
</style>

<script>
    $(document).ready(function() {
        // Toggle password visibility
        $('#togglePassword').click(function() {
            const passwordField = $('#password');
            const icon = $(this).find('i');
            
            if (passwordField.attr('type') === 'password') {
                passwordField.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                passwordField.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
        
        // Form submission with loading state
        $('#loginForm').submit(function() {
            const btn = $('#loginBtn');
            btn.prop('disabled', true);
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Signing In...');
        });
        
        // Auto-focus on username field
        $('#username').focus();
        
        // Check system status
        checkSystemStatus();
    });
    
    function checkSystemStatus() {
        $.get('<?= url('/api/system-status') ?>')
            .done(function(data) {
                if (data.status === 'online') {
                    $('#systemStatus').html('<i class="fas fa-circle text-success"></i> System Online');
                } else {
                    $('#systemStatus').html('<i class="fas fa-circle text-warning"></i> System Issues');
                }
            })
            .fail(function() {
                $('#systemStatus').html('<i class="fas fa-circle text-danger"></i> System Offline');
            });
    }
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
