<?php
/**
 * Application Configuration
 */

return [
    // Application settings
    'app' => [
        'name' => 'Client Domain & Server Manager',
        'version' => '1.0.0',
        'timezone' => 'UTC',
        'locale' => 'en',
        'debug' => DEBUG_MODE,
        'url' => IS_LOCAL ? 'http://localhost/nicetech' : 'https://yourdomain.com',
        'admin_email' => '<EMAIL>'
    ],
    
    // Security settings
    'security' => [
        'session_lifetime' => 3600, // 1 hour
        'password_min_length' => 8,
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'csrf_token_lifetime' => 3600,
        'encryption_key' => 'your-32-character-secret-key-here'
    ],
    
    // Cache settings
    'cache' => [
        'enabled' => true,
        'driver' => 'file', // file, redis, memcached
        'ttl' => 3600, // Default TTL in seconds
        'path' => CACHE_PATH
    ],
    
    // Email settings
    'mail' => [
        'driver' => 'smtp', // smtp, mail, sendmail
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'username' => '',
        'password' => '',
        'encryption' => 'tls',
        'from_address' => '<EMAIL>',
        'from_name' => 'Client Manager System'
    ],
    
    // Notification settings
    'notifications' => [
        'domain_expiry_alert_days' => [30, 15, 7, 1], // Days before expiry to send alerts
        'server_expiry_alert_days' => [30, 15, 7, 1],
        'email_notifications' => true,
        'dashboard_notifications' => true
    ],
    
    // File upload settings
    'upload' => [
        'max_size' => 5242880, // 5MB in bytes
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'path' => PUBLIC_PATH . '/uploads'
    ],
    
    // Pagination settings
    'pagination' => [
        'per_page' => 20,
        'max_per_page' => 100
    ],
    
    // Backup settings
    'backup' => [
        'enabled' => true,
        'frequency' => 'daily', // daily, weekly, monthly
        'retention_days' => 30,
        'path' => ROOT_PATH . '/backups'
    ],
    
    // API settings
    'api' => [
        'enabled' => true,
        'rate_limit' => 100, // requests per hour
        'version' => 'v1'
    ],
    
    // Theme settings
    'theme' => [
        'default' => 'light',
        'allow_user_preference' => true,
        'primary_color' => '#007bff',
        'secondary_color' => '#6c757d'
    ]
];
