<?php
/**
 * Client Domain & Server Management System
 * Entry Point - Front Controller
 * 
 * <AUTHOR> Name
 * @version 1.0
 */

// Start session
session_start();

// Define constants
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('CACHE_PATH', ROOT_PATH . '/cache');
define('DATABASE_PATH', ROOT_PATH . '/database');

// Environment detection
define('IS_LOCAL', in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1']) || 
                   strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0);

// Error reporting based on environment
if (IS_LOCAL) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    define('DEBUG_MODE', true);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    define('DEBUG_MODE', false);
}

// Autoloader
spl_autoload_register(function ($class) {
    $paths = [
        APP_PATH . '/controllers/',
        APP_PATH . '/models/',
        APP_PATH . '/middleware/',
        APP_PATH . '/core/',
        APP_PATH . '/helpers/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Include core files
require_once APP_PATH . '/core/Database.php';
require_once APP_PATH . '/core/Router.php';
require_once APP_PATH . '/core/Controller.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/core/View.php';
require_once APP_PATH . '/core/Cache.php';
require_once APP_PATH . '/core/Auth.php';
require_once APP_PATH . '/helpers/Helper.php';

// Load configuration
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';

// Initialize database and run migrations
try {
    $db = Database::getInstance();

    // Run migrations
    $db->runMigrations();

} catch (Exception $e) {
    if (DEBUG_MODE) {
        die("Database Error: " . $e->getMessage());
    } else {
        die("System temporarily unavailable. Please try again later.");
    }
}

// Initialize router
$router = new Router();

// Define routes
$router->get('/', 'HomeController@index');
$router->get('/login', 'AuthController@login');
$router->post('/login', 'AuthController@authenticate');
$router->get('/logout', 'AuthController@logout');

// Protected routes (require authentication)
$router->group(['middleware' => 'auth'], function($router) {
    // Dashboard
    $router->get('/dashboard', 'DashboardController@index');
    
    // Clients
    $router->get('/clients', 'ClientController@index');
    $router->get('/clients/create', 'ClientController@create');
    $router->post('/clients', 'ClientController@store');
    $router->get('/clients/{id}', 'ClientController@show');
    $router->get('/clients/{id}/edit', 'ClientController@edit');
    $router->put('/clients/{id}', 'ClientController@update');
    $router->delete('/clients/{id}', 'ClientController@delete');
    
    // Domains
    $router->get('/domains', 'DomainController@index');
    $router->get('/domains/create', 'DomainController@create');
    $router->post('/domains', 'DomainController@store');
    $router->get('/domains/{id}', 'DomainController@show');
    $router->get('/domains/{id}/edit', 'DomainController@edit');
    $router->put('/domains/{id}', 'DomainController@update');
    $router->delete('/domains/{id}', 'DomainController@delete');
    
    // Servers
    $router->get('/servers', 'ServerController@index');
    $router->get('/servers/create', 'ServerController@create');
    $router->post('/servers', 'ServerController@store');
    $router->get('/servers/{id}', 'ServerController@show');
    $router->get('/servers/{id}/edit', 'ServerController@edit');
    $router->put('/servers/{id}', 'ServerController@update');
    $router->delete('/servers/{id}', 'ServerController@delete');
    
    // Mediators
    $router->get('/mediators', 'MediatorController@index');
    $router->get('/mediators/create', 'MediatorController@create');
    $router->post('/mediators', 'MediatorController@store');
    $router->get('/mediators/{id}', 'MediatorController@show');
    $router->get('/mediators/{id}/edit', 'MediatorController@edit');
    $router->put('/mediators/{id}', 'MediatorController@update');
    $router->delete('/mediators/{id}', 'MediatorController@delete');
    
    // Reports
    $router->get('/reports', 'ReportController@index');
    $router->get('/reports/export', 'ReportController@export');
    
    // Settings (Admin only)
    $router->get('/settings', 'SettingsController@index');
    $router->post('/settings', 'SettingsController@update');
    
    // Users (Admin only)
    $router->get('/users', 'UserController@index');
    $router->get('/users/create', 'UserController@create');
    $router->post('/users', 'UserController@store');
    $router->get('/users/{id}/edit', 'UserController@edit');
    $router->put('/users/{id}', 'UserController@update');
    $router->delete('/users/{id}', 'UserController@delete');
});

// API routes
$router->group(['prefix' => 'api'], function($router) {
    $router->get('/clients', 'ApiController@clients');
    $router->get('/domains', 'ApiController@domains');
    $router->get('/servers', 'ApiController@servers');
    $router->get('/dashboard-stats', 'ApiController@dashboardStats');
});

// Handle the request
try {
    $router->dispatch();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die("Router Error: " . $e->getMessage());
    } else {
        // Log error and show generic message
        error_log($e->getMessage());
        http_response_code(500);
        include APP_PATH . '/views/errors/500.php';
    }
}
